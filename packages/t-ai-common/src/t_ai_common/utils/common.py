import json
from typing import Optional

import orjson


class AttachmentType:
    """
    附件类型，和前端保持一致，目前前端就这4种类型
    """

    IMAGE = "image"
    PDF = "PDF"
    MARKDOWN = "MD"
    OTHER = "FILE"


def str_to_bool(val: Optional[str] = None) -> bool:
    """to bool"""
    if val is None:
        return False

    return val.lower() in ["true", "1", "yes", "y"]


def get_module_key(service_key: str) -> str:
    return service_key.split("$")[0] if "$" in service_key else ""


def get_short_key(service_key: str) -> str:
    return service_key.split("$")[-1] if "$" in service_key else service_key


def is_sys_service(service_key: str) -> bool:
    key = service_key.split("$")[-1] if "$" in service_key else service_key
    return key.startswith("SYS_")


def obj_pretty(obj, max_length: int = 1024):
    if not obj:
        return ""
    try:
        json_bytes = orjson.dumps(obj, option=orjson.OPT_INDENT_2)
        if max_length > 0 and len(json_bytes) > max_length:
            return json_bytes[:max_length].decode(errors="ignore") + "..."
        else:
            return json_bytes.decode()
    except Exception:
        s = str(obj)
        if max_length > 0 and len(s) > max_length:
            return s[:max_length] + "..."
        return s


def parse_file_name(attachment_url):
    # 截取 ? 前的路径
    path = attachment_url.split("?")[0]

    # 提取最后一个 / 后的部分就是文件名
    file_name = path.split("/")[-1]
    return file_name


def parse_file_type(attachment_url):
    # 截取 ? 之前的部分
    path = attachment_url.split("?")[0]

    # 截取最后一个 / 后的文件名
    file_name = path.split("/")[-1]

    # 截取最后一个 . 后的后缀（不带 .）
    if "." in file_name:
        file_ext = file_name.split(".")[-1]
    else:
        file_ext = ""
    if file_ext in ["jpg", "jpeg", "png", "gif"]:
        return AttachmentType.IMAGE
    elif file_ext == "pdf":
        return AttachmentType.PDF
    elif file_ext == "md":
        return AttachmentType.MARKDOWN
    else:
        return AttachmentType.OTHER


def pretty_json(obj):
    return json.dumps(obj, ensure_ascii=False, indent=2)


def parse_file_name(attachment_url):
    # 截取 ? 前的路径
    path = attachment_url.split("?")[0]

    # 提取最后一个 / 后的部分就是文件名
    file_name = path.split("/")[-1]
    return file_name


def parse_file_type(attachment_url):
    # 截取 ? 之前的部分
    path = attachment_url.split("?")[0]

    # 截取最后一个 / 后的文件名
    file_name = path.split("/")[-1]

    # 截取最后一个 . 后的后缀（不带 .）
    if "." in file_name:
        file_ext = file_name.split(".")[-1]
    else:
        file_ext = ""
    if file_ext in ["jpg", "jpeg", "png", "gif"]:
        return AttachmentType.IMAGE
    elif file_ext == "pdf":
        return AttachmentType.PDF
    elif file_ext == "md":
        return AttachmentType.MARKDOWN
    else:
        return AttachmentType.OTHER
