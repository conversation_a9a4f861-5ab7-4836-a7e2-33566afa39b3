from typing import Any, Literal, Optional, Union

from pydantic import BaseModel, ConfigDict, Field

# 导入知识库相关DSL
from t_ai_knowledge_base.model.agent_knowledge_base_dsl import AgentKnowKnowledgeBase

AGENT_TYPE = "Agent"
REFERENCE_AGENT_TYPE = "ReferenceAgent"


# 添加一个解析函数，根据type字段选择正确的工具子类
def parse_skill_tool(data: dict):
    tool_type = data.get("type", "service")

    if tool_type == "http":
        return HttpTool.model_validate(data)
    elif tool_type == "mcp":
        # FIXME 临时兼容旧版的DSL，待所有旧DSL升级后移除该逻辑
        extend_properties = data.get("extendProperties")
        if extend_properties is not None and len(extend_properties) > 0:
            data["instructions"] = extend_properties.get("instructions")
            data["mcpServerEndpoint"] = extend_properties.get("mcpServerEndpoint")
            data["mcpServerVersion"] = extend_properties.get("mcpServerVersion")
            data["mcpServerArgs"] = extend_properties.get("mcpServerArgs")
            data["modelPublisher"] = extend_properties.get("modelPublisher")
            # data['modelProvider'] = extend_properties.get("modelProvider")
            # data['modelProviderType'] = extend_properties.get("modelProviderType")
            data["modelName"] = extend_properties.get("modelName")
        return McpTool.model_validate(data)
    elif tool_type == "service":
        return ServiceTool.model_validate(data)
    else:
        return SkillTools.model_validate(data)


def parse_triggers(data: dict):
    trigger_type = data.get("type", "crontab")

    if trigger_type == "crontab":
        return AgentSchedulerTrigger.model_validate(data)
    elif trigger_type == "event":
        return AgentEventTrigger.model_validate(data)
    else:
        return AgentSchedulerTrigger.model_validate(data)


# 添加一个解析函数
def parse_agent_element(data: dict):
    if data.get("type", AGENT_TYPE) == AGENT_TYPE:
        return AgentMeta.from_dict(data)
    else:
        return ReferenceAgentMeta.from_dict(data)


class AgentBaseModel(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True,
        validate_default=True,
        extra="ignore",
        validate_assignment=True,
    )


class Handoffs(AgentBaseModel):
    agent_key: str = Field(alias="agentKey")
    handoff_description: str = Field(alias="handoffDescription")
    feedback_after_completion: bool = Field(False, alias="feedbackAfterCompletion")


class LlmModelSetting(AgentBaseModel):
    frequency_penalty: float = Field(0.0, alias="frequencyPenalty")
    presence_penalty: float = Field(0.0, alias="presencePenalty")
    temperature: float = Field(0.0)
    top_p: int = Field(1, alias="topP")
    chat_rounds: int = Field(10, alias="chatRounds")
    max_tokens: int = Field(4096, alias="maxTokens")
    reasoning_type: Optional[str] = Field(
        None,
        alias="reasoningType",
        description="模型的推理类型，支持AUTO,NONE,REASONING",
    )

    def get_thinking_type(self) -> Optional[str]:
        """
        获取思考类型
        :return: AUTO, NONE, REASONING
        """
        if self.reasoning_type is None:
            return None
        elif self.reasoning_type == "REASONING":
            return "enabled"
        elif self.reasoning_type == "AUTO":
            return "auto"
        elif self.reasoning_type == "NONE":
            return "disabled"
        else:
            return None


class LlmModel(AgentBaseModel):
    model_publisher: Optional[str] = Field(None, alias="modelPublisher")
    name: str
    type: Optional[str] = None
    setting: LlmModelSetting


class RelatedModel(AgentBaseModel):
    model_key: str = Field(alias="modelKey")
    model_name: str = Field(alias="modelName")
    model_alias: str = Field(alias="modelAlias")


class FieldMeta(AgentBaseModel):
    field_key: str = Field(alias="fieldKey")
    field_name: str = Field(alias="fieldName")
    field_type: str = Field(alias="fieldType")
    description: Optional[str] = None
    element: Optional["FieldMeta"] = None
    elements: Optional[list["FieldMeta"]] = None
    required: bool = Field(False, alias="required")
    default_value: Any = Field(None, alias="defaultValue")
    related_model: Optional[RelatedModel] = Field(None, alias="relatedModel")


# Define ToolType as a literal type instead of a class
ToolType = Literal["service", "connector", "mcp", "http", "knowledge_base"]


class ToolVisible(AgentBaseModel):
    enabled: Optional[bool] = Field(True, alias="enabled")
    tool_arguments: Optional[bool] = Field(True, alias="toolArguments")
    tool_output: Optional[bool] = Field(True, alias="toolOutput")


class Tools(AgentBaseModel):
    key: str
    name: str
    desc: Optional[str] = None

    def is_immediately_output(self) -> bool:
        return getattr(self, 'immediately_output', False)


class SubTools(Tools):
    input: Optional[list[FieldMeta]] = Field(None, alias="input")
    final_output: bool = Field(False, alias="finalOutput")
    immediately_output: bool = Field(False, alias="immediatelyOutput",
                                     description="当为true时，该工具调用后会立即输出结果")
    tool_visible: ToolVisible = Field(default_factory=ToolVisible, alias="toolVisible")
    alias_name: Optional[str] = Field(None, alias="aliasName")

    def is_immediately_output(self) -> bool:
        return self.immediately_output


class SkillTools(Tools):
    type: ToolType


class ServiceTool(SkillTools):
    type: ToolType = "service"
    input: Optional[list[FieldMeta]] = Field(None, alias="input")
    final_output: bool = Field(False, alias="finalOutput")
    immediately_output: bool = Field(False, alias="immediatelyOutput",
                                     description="当为true时，该工具调用后会立即输出结果")
    sse_mode: Optional[bool] = Field(False, alias="sseMode")
    tool_visible: ToolVisible = Field(default_factory=ToolVisible, alias="toolVisible")

    def is_immediately_output(self) -> bool:
        return self.immediately_output


class McpTool(SkillTools):
    type: ToolType = "mcp"
    instructions: Optional[str] = Field(None, alias="instructions")
    extend_properties: Optional[dict[str, Any]] = Field(None, alias="extendProperties", deprecated=True)  # Deprecated
    mcp_server_endpoint: str = Field(alias="mcpServerEndpoint")
    mcp_server_version: Optional[str] = Field(None, alias="mcpServerVersion")
    mcp_server_args: Optional[dict] = Field(None, alias="mcpServerArgs")
    model_publisher: Optional[str] = Field(None, alias="modelPublisher")
    model_name: Optional[str] = Field(None, alias="modelName")
    use_mode: Literal["agent_as_tool", "mcp_server"] = Field("mcp_server", alias="use_mode")
    tools: list[SubTools] = Field(default_factory=list)
    source_type: Optional[Literal["ai-proxy", "built-in", "third-party"]] = Field(
        default=None,
        alias="sourceType",
        description="MCP工具的的类型",
    )
    lazy: bool = Field(default=True, alias="lazy", description="是否懒加载MCP工具，默认为True")


class HttpTool(SkillTools):
    type: ToolType = "http"
    url: str
    method: str
    content_type: str = Field("application/json", alias="contentType")
    headers: Optional[list[Any]] = None
    path_variables: Optional[list[Any]] = Field(None, alias="pathVariables")
    params: Optional[list[Any]] = None
    body_type: str = Field("JSON", alias="bodyType")
    body: Optional[Any] = None
    json_body: Optional[str | list[FieldMeta]] = Field(None, alias="jsonBody")
    final_output: bool = Field(False, alias="finalOutput")
    immediately_output: bool = Field(False, alias="immediatelyOutput",
                                     description="当为true时，该工具调用后会立即输出结果")
    tool_visible: ToolVisible = Field(default_factory=ToolVisible, alias="toolVisible")

    def is_immediately_output(self) -> bool:
        return self.immediately_output


class NotificationConfig(AgentBaseModel):
    notice_scene_code: Optional[str] = Field(None, alias="noticeSceneCode")
    notice_scene_name: Optional[str] = Field(None, alias="noticeSceneName")
    variables: Optional[list[dict]] = Field(None, alias="variables")
    receiver_users: Optional[list[dict]] = Field(None, alias="receiverUsers")
    receiver_user_ids: Optional[list[dict]] = Field(None, alias="receiverUserIds")
    receiver_mobiles: Optional[list[dict]] = Field(None, alias="receiverMobiles")
    receiver_emails: Optional[list[dict]] = Field(None, alias="receiverEmails")
    receiver_station_letter_ids: Optional[list[dict]] = Field(None, alias="receiverStationLetterIds")
    wecom_robot_key: Optional[str] = Field(None, alias="wecomRobotKey")


class AgentTrigger(AgentBaseModel):
    key: Optional[str]
    name: Optional[str]
    desc: Optional[str] = None
    conversation_content: Optional[str] = Field(None, alias="conversationContent")
    tool: Optional[Union[ServiceTool, HttpTool, McpTool, SubTools]] = Field(None, alias="tool")
    notification_config: Optional[NotificationConfig] = Field(None, alias="notificationConfig")


class AgentSchedulerTrigger(AgentTrigger):
    crontab: Optional[str] = Field(None, alias="crontab")


class AgentEventTrigger(AgentTrigger):
    event_key: Optional[str] = Field(None, alias="eventKey")


class AgentMetaProperties(AgentBaseModel):
    avatar: Optional[str] = None
    model: LlmModel
    greetings: Optional[str] = None
    user_questions: Optional[list[str]] = Field(None, alias="userQuestions")
    user_questions_suggest: bool = Field(False, alias="userQuestionsSuggest")
    user_questions_suggestion_prompt: Optional[str] = Field(None, alias="userQuestionsSuggestionPrompt")
    system_prompt: str = Field(alias="systemPrompt")
    skill_tools: Optional[list[Union[ServiceTool, HttpTool, McpTool, SubTools]]] = Field(
        None, alias="skillTools"
    )
    triggers: Optional[list[Union[AgentSchedulerTrigger, AgentEventTrigger]]] = Field(
        None, alias="triggers"
    )
    knowledge_base: Optional[AgentKnowKnowledgeBase] = Field(
        None, alias="knowledgeBase"
    )
    related_models: Optional[list[RelatedModel]] = Field(None, alias="relatedModels")
    input: Optional[list[FieldMeta]] = Field(None, alias="input")
    # reasoning_framework: Optional[str] = Field(None, alias="reasoningFramework", description="推理框架")
    # check_result: Optional[bool] = Field(None, alias="checkResult", description="是否需要验证结果")
    reply_with_user_language: Optional[bool] = Field(
        None, alias="replyWithUserLanguage", description="是否使用用户语言回复"
    )

    @classmethod
    def from_dict(cls, data: dict):
        # 处理 skill_tools 字段，根据类型选择正确的子类
        if "skillTools" in data and data["skillTools"]:
            skill_tools = []
            for tool_data in data["skillTools"]:
                skill_tools.append(parse_skill_tool(tool_data))
            data["skillTools"] = skill_tools

        # 处理 knowledge_base 字段
        if "knowledgeBase" in data and data["knowledgeBase"]:
            data["knowledgeBase"] = AgentKnowKnowledgeBase.model_validate(data["knowledgeBase"])

        # 处理 triggers 字段
        if "triggers" in data and data["triggers"]:
            triggers = []
            for trigger in data["triggers"]:
                triggers.append(parse_triggers(trigger))
            data["triggers"] = triggers

        return cls.model_validate(data)


class ReferenceAgentMetaProperties(AgentBaseModel):
    ref_agent_key: str = Field(alias="refAgentKey")
    ref_agent_name: str = Field(alias="refAgentName")


class AgentElement(AgentBaseModel):
    type: str
    id: Optional[str] = None
    key: str
    name: str
    desc: Optional[str] = None
    handoffs: Optional[list[Handoffs]] = None
    children: Optional[list[Union["AgentMeta", "ReferenceAgentMeta"]]] = None

    @classmethod
    def from_dict(cls, data: dict):
        if "children" in data and data["children"]:
            children = []
            for child in data["children"]:
                parsed_child = parse_agent_element(child)
                if parsed_child:
                    children.append(parsed_child)
            data["children"] = children

        return cls.model_validate(data)


class AgentMeta(AgentElement):
    type: str = AGENT_TYPE
    props: AgentMetaProperties

    @classmethod
    def from_dict(cls, data: dict):
        # 处理 children 字段
        if "children" in data and data["children"]:
            children = []
            for child in data["children"]:
                parsed_child = parse_agent_element(child)
                if parsed_child:
                    children.append(parsed_child)
            data["children"] = children

        # 处理 props 字段，使用 AgentMetaProperties.from_dict
        if "props" in data:
            data["props"] = AgentMetaProperties.from_dict(data["props"])

        return cls.model_validate(data)


class ReferenceAgentMeta(AgentElement):
    type: str = REFERENCE_AGENT_TYPE
    props: ReferenceAgentMetaProperties
    target_agent: Optional[AgentMeta] = Field(None, alias="targetAgent")

    @classmethod
    def from_dict(cls, data: dict):
        # 处理 children 字段
        if "children" in data and data["children"]:
            children = []
            for child in data["children"]:
                parsed_child = parse_agent_element(child)
                if parsed_child:
                    children.append(parsed_child)
            data["children"] = children

        # 处理 target_agent 字段
        if "targetAgent" in data and data["targetAgent"]:
            data["targetAgent"] = AgentMeta.from_dict(data["targetAgent"])

        return cls.model_validate(data)
